<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use App\Services\TripayServices;
use App\Models\Invoice;

class TripayController extends Controller
{
    /**
     * Get payment instructions for a specific payment method
     *
     * @param string $code Payment method code
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPaymentInstructions($code)
    {
        $tripay = new TripayServices();
        $instructions = $tripay->getPaymentInstructions($code);

        return response()->json($instructions);
    }

    public function getPaymentChannels()
    {
        $tripay = new TripayServices();
        $channels = $tripay->getPaymentChannels();

        return response()->json($channels);
    }

    public function showPaymentPage($id)
    {
        // Get the invoice
        $invoice = Invoice::findOrFail($id);

        // Get payment channels
        $tripay = new TripayServices();
        $channels = $tripay->getPaymentChannels();

        // Filter channels to only include the ones we want to display
        $allowedChannels = ['BRIVA', 'MANDIRIVA', 'BNIVA', 'QRIS', 'DANA', 'GOPAY', 'OVO'];
        $filteredChannels = array_filter($channels, function($channel) use ($allowedChannels) {
            return in_array($channel['code'], $allowedChannels);
        });

        return view('/pelanggan/payment/invoice', [
            'invoice' => $invoice,
            'channels' => $filteredChannels,
            'users' => auth()->user(),
            'roles' => auth()->user()->roles,
        ]);
    }

    /**
     * Process payment for an invoice
     *
     * @param Request $request
     * @param int $id Invoice ID
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processPayment(Request $request, $id)
    {
        // Validate the request
        // dd($request->all());
        $request->validate([
            'payment_method' => 'required|string',
        ]);

        try {
            // Get the invoice
            $invoice = Invoice::findOrFail($id);

            // Check if invoice is already paid
            if ($invoice->status_id == 8) { // Assuming 8 is the paid status
                return redirect()->back()->with('info', 'Invoice ini sudah dibayar.');
            }

            // Create a new TripayServices instance
            $tripay = new TripayServices();

            // Process the payment
            $paymentMethod = $request->payment_method;
            // Log the payment attempt
            \Log::info('Payment attempt', [
                'invoice_id' => $id,
                'payment_method' => $paymentMethod,
                'amount' => $invoice->tagihan
            ]);

            // Create transaction in Tripay
            $transaction = $tripay->createTransaction($invoice, $paymentMethod);

            // Check if transaction was created successfully
            if (isset($transaction['success']) && $transaction['success'] && isset($transaction['data'])) {
                // Store the transaction reference in session for later use
                session(['last_transaction_reference' => $transaction['data']['reference']]);

                // Redirect to payment page
                return redirect($transaction['data']['checkout_url']);
            }

            // Log the error
            \Log::error('Payment processing error', [
                'invoice_id' => $id,
                'payment_method' => $paymentMethod,
                'response' => $transaction
            ]);

            // If there was an error, redirect back with error message
            $errorMessage = isset($transaction['message'])
                ? 'Error: ' . $transaction['message']
                : 'Terjadi kesalahan saat memproses pembayaran. Silakan coba lagi.';

            return redirect()->back()->with('error', $errorMessage);
        } catch (\Exception $e) {
            \Log::error('Exception in payment processing', [
                'invoice_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Handle payment callback from Tripay
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function paymentCallback(Request $request)
    {
        try {
            // Disable CSRF protection for this request
            app('session')->forget('_token');

            // Log the callback request for debugging
            \Log::info('Payment callback received', [
                'headers' => $request->headers->all(),
                'body' => $request->getContent(),
                'all' => $request->all(),
                'route' => $request->route()->getName(),
                'url' => $request->url(),
                'method' => $request->method()
            ]);

            // Get the callback signature
            $callbackSignature = $request->header('X-Callback-Signature');

            // Check if this is a test mode request first
            if ($request->has('test_mode')) {
                // Use the request data directly for test mode
                $data = (object) $request->all();
                $invoiceId = $request->input('invoice_id');

                \Log::info('Test mode callback', ['data' => $data]);
            } else {
                // Get the JSON data for normal Tripay callbacks
                $json = $request->getContent();

                // Check if we have JSON content
                if (empty($json)) {
                    // If no JSON and not test mode, try to use form data
                    \Log::warning('No JSON content in callback', ['request' => $request->all()]);

                    // Try to get data from request parameters
                    $data = (object) $request->all();
                    $invoiceId = $request->input('invoice_id');

                    // If no invoice_id in request, check for merchant_ref
                    $merchantRef = $request->input('merchant_ref');
                    if (!$invoiceId && $merchantRef) {
                        $parts = explode('-', $merchantRef);
                        $invoiceId = $parts[1] ?? null;

                        \Log::info('Extracted invoice ID from form merchant_ref', [
                            'merchant_ref' => $merchantRef,
                            'invoice_id' => $invoiceId
                        ]);
                    }
                } else {
                    // Normal flow with JSON data
                    // Validate the callback signature if provided
                    if ($callbackSignature) {
                        $privateKey = config('tripay.private_key');
                        $signature = hash_hmac('sha256', $json, $privateKey);

                        // Log signature comparison for debugging
                        \Log::info('Signature comparison', [
                            'expected' => $signature,
                            'received' => $callbackSignature,
                            'match' => ($signature === $callbackSignature),
                            'environment' => env('APP_ENV'),
                            'is_sandbox' => env('APP_ENV') !== 'production'
                        ]);

                        // For sandbox mode, be more lenient with signature validation
                        $isSandbox = env('APP_ENV') !== 'production';

                        if ($signature !== $callbackSignature && !$request->has('test_mode') && !$isSandbox) {
                            \Log::warning('Invalid signature in production mode', [
                                'expected' => $signature,
                                'received' => $callbackSignature
                            ]);

                            return response()->json([
                                'success' => false,
                                'message' => 'Invalid signature',
                            ], 400);
                        } elseif ($signature !== $callbackSignature && $isSandbox) {
                            \Log::info('Signature mismatch in sandbox mode - continuing anyway', [
                                'expected' => $signature,
                                'received' => $callbackSignature
                            ]);
                        }
                    }

                    // Decode the JSON
                    $data = json_decode($json);

                    if (!$data) {
                        \Log::error('Failed to decode JSON data', ['json' => $json]);

                        // Try to continue with request data as fallback
                        $data = (object) $request->all();
                        $invoiceId = $request->input('invoice_id');
                    } else {
                        // Extract the merchant_ref to get the invoice ID
                        $merchantRef = $data->merchant_ref ?? '';
                        $reference = $data->reference ?? '';

                        // For direct testing with invoice ID
                        $invoiceId = $request->input('invoice_id');

                        // Try to extract from merchant_ref if available and invoice_id not directly provided
                        if (!$invoiceId && !empty($merchantRef)) {
                            $parts = explode('-', $merchantRef);
                            // The invoice ID should be the second part after splitting by '-'
                            if (isset($parts[1])) {
                                $invoiceId = $parts[1];
                                \Log::info('Extracted invoice ID from merchant_ref', [
                                    'merchant_ref' => $merchantRef,
                                    'invoice_id' => $invoiceId
                                ]);
                            } else {
                                \Log::warning('Could not extract invoice ID from merchant_ref', [
                                    'merchant_ref' => $merchantRef
                                ]);
                            }
                        }

                        // If still no invoice ID, try to find the invoice by reference in our database
                        if (!$invoiceId && !empty($reference)) {
                            \Log::info('Trying to find invoice by reference', [
                                'reference' => $reference
                            ]);

                            // Try to find the invoice by reference in our database
                            $invoiceByRef = Invoice::where('reference', $reference)->first();

                            if ($invoiceByRef) {
                                $invoiceId = $invoiceByRef->id;
                                \Log::info('Found invoice by reference in database', [
                                    'reference' => $reference,
                                    'invoice_id' => $invoiceId
                                ]);
                            } else {
                                // If not found in database, try to find by merchant_ref
                                if (!empty($merchantRef)) {
                                    $invoiceByMerchantRef = Invoice::where('merchant_ref', $merchantRef)->first();

                                    if ($invoiceByMerchantRef) {
                                        $invoiceId = $invoiceByMerchantRef->id;
                                        \Log::info('Found invoice by merchant_ref in database', [
                                            'merchant_ref' => $merchantRef,
                                            'invoice_id' => $invoiceId
                                        ]);
                                    }
                                }

                                // If still not found, try to get details from Tripay API
                                if (!$invoiceId) {
                                    // Get transaction details from Tripay using the reference
                                    $tripay = new TripayServices();
                                    $transactionDetails = $tripay->getTransactionDetails($reference);

                                    if (isset($transactionDetails['data']['merchant_ref'])) {
                                        $merchantRefFromApi = $transactionDetails['data']['merchant_ref'];
                                        $parts = explode('-', $merchantRefFromApi);
                                        if (isset($parts[1])) {
                                            $invoiceId = $parts[1];
                                            \Log::info('Found invoice ID from Tripay API', [
                                                'reference' => $reference,
                                                'merchant_ref' => $merchantRefFromApi,
                                                'invoice_id' => $invoiceId
                                            ]);

                                            // Update the invoice with the reference and merchant_ref for future use
                                            try {
                                                $invoice = Invoice::find($invoiceId);
                                                if ($invoice) {
                                                    $invoice->reference = $reference;
                                                    $invoice->merchant_ref = $merchantRefFromApi;
                                                    $invoice->save();

                                                    \Log::info('Updated invoice with reference data', [
                                                        'invoice_id' => $invoiceId,
                                                        'reference' => $reference,
                                                        'merchant_ref' => $merchantRefFromApi
                                                    ]);
                                                }
                                            } catch (\Exception $e) {
                                                \Log::error('Failed to update invoice with reference data', [
                                                    'invoice_id' => $invoiceId,
                                                    'error' => $e->getMessage()
                                                ]);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (!$invoiceId) {
                // Log more detailed information to help diagnose the issue
                \Log::warning('Invoice ID not found in request', [
                    'data' => $request->all(),
                    'json_content' => $request->getContent(),
                    'headers' => $request->headers->all(),
                    'merchant_ref' => $data->merchant_ref ?? null,
                    'reference' => $data->reference ?? null
                ]);

                // Try one more fallback - check if we have any recent invoices with matching amount
                if (isset($data->amount) && $data->amount > 0) {
                    $possibleInvoice = Invoice::where('tagihan', $data->amount)
                        ->where('created_at', '>=', now()->subDays(7))
                        ->orderBy('created_at', 'desc')
                        ->first();

                    if ($possibleInvoice) {
                        $invoiceId = $possibleInvoice->id;
                        \Log::info('Found possible matching invoice by amount', [
                            'amount' => $data->amount,
                            'invoice_id' => $invoiceId
                        ]);
                    }
                }

                // If we still don't have an invoice ID, return an error
                if (!$invoiceId) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invoice ID not found',
                        'data_received' => $request->all()
                    ], 400);
                }
            }

            // Get the invoice
            $invoice = Invoice::find($invoiceId);

            if (!$invoice) {
                \Log::warning('Invoice not found', ['invoice_id' => $invoiceId]);
                return response()->json([
                    'success' => false,
                    'message' => 'Invoice not found',
                ], 404);
            }

            // Get payment status from data
            $paymentStatus = null;

            // Try to get status from different possible sources
            if (isset($data->status)) {
                $paymentStatus = $data->status;
            } elseif ($request->has('status')) {
                $paymentStatus = $request->input('status');
            }

            // For test mode, always consider it as PAID
            if ($request->has('test_mode')) {
                $paymentStatus = 'PAID';
            }

            \Log::info('Payment status check', [
                'invoice_id' => $invoice->id,
                'status' => $paymentStatus,
                'test_mode' => $request->has('test_mode')
            ]);

            // Update the invoice status based on the payment status
            // For testing, we'll consider it paid if status is PAID or if test_mode is set
            if ($paymentStatus === 'PAID' || $request->has('test_mode')) {
                // Check if invoice is already paid
                if ($invoice->status_id == 8) {
                    \Log::info('Invoice already marked as paid', [
                        'invoice_id' => $invoice->id
                    ]);
                } else {
                    // Update the invoice status to paid (assuming status_id 8 is for paid)
                    $invoice->status_id = 8; // Adjust this based on your status IDs
                    $invoice->save();

                    \Log::info('Invoice marked as paid', [
                        'invoice_id' => $invoice->id,
                        'customer' => $invoice->customer->nama_customer,
                        'amount' => $invoice->tagihan
                    ]);

                    // You might want to add more logic here, such as sending a notification to the customer
                    // For example, you could dispatch a job to send an email or notification

                    // Example: event(new InvoicePaid($invoice));
                }
            } else {
                \Log::info('Payment status not PAID', [
                    'invoice_id' => $invoice->id,
                    'status' => $paymentStatus ?? 'unknown'
                ]);

                // For EXPIRED or FAILED status, you might want to update the invoice status accordingly
                if ($paymentStatus === 'EXPIRED' || $paymentStatus === 'FAILED') {
                    // You could update the invoice status to reflect the payment failure
                    // $invoice->status_id = 9; // Assuming 9 is for failed payments
                    // $invoice->save();

                    \Log::info('Payment failed or expired', [
                        'invoice_id' => $invoice->id,
                        'status' => $paymentStatus
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Callback processed successfully',
                'invoice_id' => $invoice->id,
                'status' => $invoice->status_id == 8 ? 'paid' : 'pending'
            ]);
        } catch (\Exception $e) {
            \Log::error('Error processing payment callback', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error: ' . $e->getMessage(),
            ], 500);
        }
        dd($response);
    }

    /**
     * Show the callback tester form
     */
    public function showCallbackTester()
    {
        // Get all invoices for testing
        $invoices = Invoice::where('status_id', '!=', 8)->get();

        return view('payment.callback-tester', [
            'invoices' => $invoices,
            'callbackUrl' => route('payment.callback'),
            'users' => auth()->user(),
            'roles' => auth()->user()->roles,
        ]);
    }

    /**
     * Process the callback test
     */
    public function processCallbackTest(Request $request)
    {
        // dd($request->all());
        // Validate the request but don't use 'exists' validation to avoid table name issues
        $request->validate([
            'invoice_id' => 'required',
        ]);

        $invoice = Invoice::find($request->invoice_id);

        if (!$invoice) {
            return redirect()->back()->with('error', 'Invoice not found');
        }

        try {
            // Update the invoice status directly
            $invoice->status_id = 8; // Paid status
            $invoice->save();

            \Log::info('Invoice marked as paid via test callback', ['invoice_id' => $invoice->id]);

            // Check if the request is coming from the invoice page
            $referer = request()->headers->get('referer');
            if (strpos($referer, 'payment/invoice') !== false) {
                // If coming from invoice page, redirect to the same page to show updated status
                return redirect()->route('payment.show', $invoice->id)
                    ->with('success', 'Pembayaran berhasil! Invoice #' . $invoice->id . ' telah ditandai sebagai lunas.');
            }

            // Otherwise, redirect back to the callback tester page
            return redirect()->back()->with('success', 'Callback test successful! Invoice #' . $invoice->id . ' marked as paid.');
        } catch (\Exception $e) {
            \Log::error('Error in callback test', ['error' => $e->getMessage()]);
            return redirect()->back()->with('error', 'Callback test failed: ' . $e->getMessage());
        }
    }

    /**
     * Simulate sandbox payment completion
     * This method simulates what Tripay would send in a real callback
     */
    public function simulateSandboxPayment($invoiceId)
    {
        try {
            $invoice = Invoice::find($invoiceId);

            if (!$invoice) {
                \Log::error('Invoice not found for sandbox simulation', ['invoice_id' => $invoiceId]);
                return response()->json(['success' => false, 'message' => 'Invoice not found'], 404);
            }

            // Create a simulated Tripay callback payload
            $simulatedPayload = [
                'reference' => $invoice->reference ?? 'SANDBOX-' . time(),
                'merchant_ref' => $invoice->merchant_ref ?? 'INV-' . $invoice->id . '-' . time(),
                'payment_method' => 'SANDBOX',
                'payment_method_code' => 'SANDBOX',
                'total_amount' => $invoice->tagihan,
                'fee_merchant' => 0,
                'fee_customer' => 0,
                'total_fee' => 0,
                'amount_received' => $invoice->tagihan,
                'is_closed_payment' => 1,
                'status' => 'PAID',
                'paid_at' => time(),
                'note' => 'Sandbox payment simulation'
            ];

            // Create a request object with the simulated data
            $request = new \Illuminate\Http\Request();
            $request->merge($simulatedPayload);
            $request->merge(['test_mode' => true]);

            // Call the callback handler
            $response = $this->paymentCallback($request);

            return $response;
        } catch (\Exception $e) {
            \Log::error('Error in sandbox payment simulation', [
                'invoice_id' => $invoiceId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Simulation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check payment status from Tripay API and update invoice accordingly
     */
    public function checkPaymentStatus($invoiceId)
    {
        try {
            $invoice = Invoice::find($invoiceId);

            if (!$invoice) {
                return response()->json(['success' => false, 'message' => 'Invoice not found'], 404);
            }

            if (!$invoice->reference) {
                return response()->json(['success' => false, 'message' => 'No Tripay reference found for this invoice'], 400);
            }

            // Get transaction details from Tripay
            $tripay = new TripayServices();
            $transactionDetails = $tripay->getTransactionDetails($invoice->reference);

            \Log::info('Checking payment status from Tripay API', [
                'invoice_id' => $invoiceId,
                'reference' => $invoice->reference,
                'response' => $transactionDetails
            ]);

            if (isset($transactionDetails['success']) && $transactionDetails['success'] && isset($transactionDetails['data'])) {
                $tripayData = $transactionDetails['data'];
                $status = $tripayData['status'] ?? 'UNKNOWN';

                // Update invoice status based on Tripay status
                if ($status === 'PAID') {
                    if ($invoice->status_id != 8) {
                        $invoice->status_id = 8; // Paid status
                        $invoice->save();

                        \Log::info('Invoice status updated to PAID from Tripay API check', [
                            'invoice_id' => $invoiceId,
                            'tripay_status' => $status
                        ]);
                    }

                    return response()->json([
                        'success' => true,
                        'message' => 'Payment confirmed as PAID',
                        'status' => 'PAID',
                        'invoice_status' => 'paid'
                    ]);
                } else {
                    return response()->json([
                        'success' => true,
                        'message' => 'Payment status checked',
                        'status' => $status,
                        'invoice_status' => $invoice->status_id == 8 ? 'paid' : 'pending'
                    ]);
                }
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to get transaction details from Tripay',
                    'response' => $transactionDetails
                ], 400);
            }
        } catch (\Exception $e) {
            \Log::error('Error checking payment status', [
                'invoice_id' => $invoiceId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error checking payment status: ' . $e->getMessage()
            ], 500);
        }
    }
}
